import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class EnhancedCategoryBreakdownWidget extends StatefulWidget {
  final List<Map<String, dynamic>> categoryData;
  final List<Map<String, dynamic>> trendsData;

  const EnhancedCategoryBreakdownWidget({
    super.key,
    required this.categoryData,
    required this.trendsData,
  });

  @override
  State<EnhancedCategoryBreakdownWidget> createState() => _EnhancedCategoryBreakdownWidgetState();
}

class _EnhancedCategoryBreakdownWidgetState extends State<EnhancedCategoryBreakdownWidget> {
  int _selectedView = 0; // 0: Pie Chart, 1: Bar Chart, 2: Trends
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme, isDark),
          SizedBox(height: 2.h),
          _buildCategoryStats(theme, isDark),
          SizedBox(height: 3.h),
          _buildVisualization(theme, isDark),
          SizedBox(height: 3.h),
          _buildCategoryList(theme, isDark),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDark) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Category Breakdown',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                color: isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
              ),
            ),
            SizedBox(height: 0.5.h),
            Text(
              'Detailed spending analysis by category',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
              ),
            ),
          ],
        ),
        _buildViewSelector(theme, isDark),
      ],
    );
  }

  Widget _buildViewSelector(ThemeData theme, bool isDark) {
    final views = [
      {'icon': Icons.pie_chart, 'label': 'Pie'},
      {'icon': Icons.bar_chart, 'label': 'Bar'},
      {'icon': Icons.show_chart, 'label': 'Trends'},
    ];

    return Container(
      padding: EdgeInsets.all(1.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: Row(
        children: List.generate(views.length, (index) {
          final isSelected = index == _selectedView;
          return GestureDetector(
            onTap: () => setState(() => _selectedView = index),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
              decoration: BoxDecoration(
                color: isSelected
                    ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    views[index]['icon'] as IconData,
                    size: 16,
                    color: isSelected
                        ? Colors.white
                        : (isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight),
                  ),
                  SizedBox(width: 1.w),
                  Text(
                    views[index]['label'] as String,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isSelected
                          ? Colors.white
                          : (isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight),
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildCategoryStats(ThemeData theme, bool isDark) {
    if (widget.categoryData.isEmpty) return const SizedBox.shrink();

    final totalAmount = widget.categoryData.fold<double>(
      0.0,
      (sum, category) => sum + (category['amount'] as double? ?? 0.0),
    );

    final topCategory = widget.categoryData.first;
    final categoryCount = widget.categoryData.length;
    final avgAmount = totalAmount / categoryCount;

    final stats = [
      {
        'title': 'Total Spending',
        'value': '\$${totalAmount.toStringAsFixed(0)}',
        'icon': Icons.account_balance_wallet,
        'color': isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
      },
      {
        'title': 'Top Category',
        'value': topCategory['categoryName'] ?? topCategory['name'] ?? 'Unknown',
        'icon': Icons.star,
        'color': AppTheme.getWarningColor(isDark),
      },
      {
        'title': 'Categories',
        'value': categoryCount.toString(),
        'icon': Icons.category,
        'color': AppTheme.getSuccessColor(isDark),
      },
      {
        'title': 'Average',
        'value': '\$${avgAmount.toStringAsFixed(0)}',
        'icon': Icons.trending_up,
        'color': AppTheme.getErrorColor(isDark),
      },
    ];

    return Row(
      children: stats.map((stat) {
        return Expanded(
          child: Container(
            margin: EdgeInsets.only(right: stats.indexOf(stat) < stats.length - 1 ? 2.w : 0),
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: (stat['color'] as Color).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: (stat['color'] as Color).withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              children: [
                Icon(
                  stat['icon'] as IconData,
                  color: stat['color'] as Color,
                  size: 20,
                ),
                SizedBox(height: 1.h),
                Text(
                  stat['title'] as String,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 0.5.h),
                Text(
                  stat['value'] as String,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildVisualization(ThemeData theme, bool isDark) {
    return Container(
      height: 35.h,
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: _buildSelectedVisualization(theme, isDark),
    );
  }

  Widget _buildSelectedVisualization(ThemeData theme, bool isDark) {
    switch (_selectedView) {
      case 0:
        return _buildPieChart(theme, isDark);
      case 1:
        return _buildBarChart(theme, isDark);
      case 2:
        return _buildTrendsChart(theme, isDark);
      default:
        return _buildPieChart(theme, isDark);
    }
  }

  Widget _buildPieChart(ThemeData theme, bool isDark) {
    if (widget.categoryData.isEmpty) {
      return Center(
        child: Text(
          'No category data available',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
          ),
        ),
      );
    }

    return Row(
      children: [
        Expanded(
          flex: 2,
          child: PieChart(
            PieChartData(
              pieTouchData: PieTouchData(
                touchCallback: (FlTouchEvent event, pieTouchResponse) {
                  setState(() {
                    if (!event.isInterestedForInteractions ||
                        pieTouchResponse == null ||
                        pieTouchResponse.touchedSection == null) {
                      touchedIndex = -1;
                      return;
                    }
                    touchedIndex = pieTouchResponse.touchedSection!.touchedSectionIndex;
                  });
                },
              ),
              borderData: FlBorderData(show: false),
              sectionsSpace: 2,
              centerSpaceRadius: 15.w,
              sections: _buildPieChartSections(isDark),
            ),
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          flex: 3,
          child: _buildPieChartLegend(theme, isDark),
        ),
      ],
    );
  }

  List<PieChartSectionData> _buildPieChartSections(bool isDark) {
    return widget.categoryData.asMap().entries.map((entry) {
      final index = entry.key;
      final category = entry.value;
      final isTouched = index == touchedIndex;
      final fontSize = isTouched ? 14.sp : 12.sp;
      final radius = isTouched ? 25.w : 22.w;

      return PieChartSectionData(
        color: _getCategoryColor(category['categoryName'] ?? category['name'] ?? 'Unknown'),
        value: category['amount'] as double? ?? 0.0,
        title: '${(category['percentage'] as double? ?? 0.0).toStringAsFixed(1)}%',
        radius: radius,
        titleStyle: TextStyle(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildPieChartLegend(ThemeData theme, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widget.categoryData.map((category) {
        return Container(
          margin: EdgeInsets.only(bottom: 1.h),
          child: Row(
            children: [
              Container(
                width: 3.w,
                height: 3.w,
                decoration: BoxDecoration(
                  color: _getCategoryColor(category['categoryName'] ?? category['name'] ?? 'Unknown'),
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category['categoryName'] ?? category['name'] ?? 'Unknown',
                      style: theme.textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '\$${(category['amount'] as double? ?? 0.0).toStringAsFixed(0)}',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  Widget _buildBarChart(ThemeData theme, bool isDark) {
    if (widget.categoryData.isEmpty) {
      return Center(
        child: Text(
          'No category data available',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
          ),
        ),
      );
    }

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxCategoryAmount() * 1.1,
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < widget.categoryData.length) {
                  final categoryName = widget.categoryData[value.toInt()]['categoryName'] ?? 
                                     widget.categoryData[value.toInt()]['name'] ?? 'Unknown';
                  return Text(
                    categoryName.length > 8 ? '${categoryName.substring(0, 8)}...' : categoryName,
                    style: theme.textTheme.bodySmall,
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  '\$${(value / 1000).toStringAsFixed(0)}k',
                  style: theme.textTheme.bodySmall,
                );
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        barGroups: widget.categoryData.asMap().entries.map((entry) {
          return BarChartGroupData(
            x: entry.key,
            barRods: [
              BarChartRodData(
                toY: entry.value['amount'] as double? ?? 0.0,
                color: _getCategoryColor(entry.value['categoryName'] ?? entry.value['name'] ?? 'Unknown'),
                width: 4.w,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTrendsChart(ThemeData theme, bool isDark) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.show_chart,
            size: 48,
            color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
          ),
          SizedBox(height: 2.h),
          Text(
            'Category Trends',
            style: theme.textTheme.titleMedium?.copyWith(
              color: isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Coming soon - Track category spending over time',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryList(ThemeData theme, bool isDark) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Category Details',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2.h),
          ...widget.categoryData.take(5).map((category) => Container(
            margin: EdgeInsets.only(bottom: 2.h),
            child: Row(
              children: [
                Container(
                  width: 4.w,
                  height: 4.w,
                  decoration: BoxDecoration(
                    color: _getCategoryColor(category['categoryName'] ?? category['name'] ?? 'Unknown'),
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            category['categoryName'] ?? category['name'] ?? 'Unknown',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '\$${(category['amount'] as double? ?? 0.0).toStringAsFixed(0)}',
                            style: AppTheme.currencyStyle(
                              isLight: !isDark,
                              fontSize: 14.sp,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 0.5.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${(category['percentage'] as double? ?? 0.0).toStringAsFixed(1)}% of total',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
                            ),
                          ),
                          Text(
                            '${category['transactionCount'] ?? 0} transactions',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Color _getCategoryColor(String categoryName) {
    final colors = {
      'Food & Dining': const Color(0xFF4CAF50),
      'Food': const Color(0xFF4CAF50),
      'Transportation': const Color(0xFF2196F3),
      'Transport': const Color(0xFF2196F3),
      'Shopping': const Color(0xFFFF9800),
      'Bills & Utilities': const Color(0xFFF44336),
      'Bills': const Color(0xFFF44336),
      'Utilities': const Color(0xFFF44336),
      'Entertainment': const Color(0xFF9C27B0),
      'Healthcare': const Color(0xFF607D8B),
      'Health': const Color(0xFF607D8B),
      'Education': const Color(0xFF795548),
      'Travel': const Color(0xFF00BCD4),
      'Business': const Color(0xFF3F51B5),
      'Investment': const Color(0xFF8BC34A),
      'Salary': const Color(0xFF4CAF50),
      'Freelance': const Color(0xFF2196F3),
      'Other': const Color(0xFF9E9E9E),
    };

    return colors[categoryName] ?? const Color(0xFF9E9E9E);
  }

  double _getMaxCategoryAmount() {
    if (widget.categoryData.isEmpty) return 1000;
    
    double max = 0;
    for (final category in widget.categoryData) {
      final amount = category['amount'] as double? ?? 0.0;
      if (amount > max) max = amount;
    }
    return max;
  }
}
