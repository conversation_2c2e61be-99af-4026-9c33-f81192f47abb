import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class CashFlowAnalysisWidget extends StatefulWidget {
  final List<Map<String, dynamic>> cashFlowData;
  final Map<String, dynamic> financialPerformance;

  const CashFlowAnalysisWidget({
    super.key,
    required this.cashFlowData,
    required this.financialPerformance,
  });

  @override
  State<CashFlowAnalysisWidget> createState() => _CashFlowAnalysisWidgetState();
}

class _CashFlowAnalysisWidgetState extends State<CashFlowAnalysisWidget> {
  int _selectedPeriod = 0; // 0: Daily, 1: Weekly, 2: Monthly

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme, isDark),
          SizedBox(height: 2.h),
          _buildCashFlowMetrics(theme, isDark),
          SizedBox(height: 3.h),
          _buildCashFlowChart(theme, isDark),
          SizedBox(height: 3.h),
          _buildLiquidityAnalysis(theme, isDark),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDark) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Cash Flow Analysis',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                color: isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
              ),
            ),
            SizedBox(height: 0.5.h),
            Text(
              'Track your money movement patterns',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
              ),
            ),
          ],
        ),
        _buildPeriodSelector(theme, isDark),
      ],
    );
  }

  Widget _buildPeriodSelector(ThemeData theme, bool isDark) {
    final periods = ['Daily', 'Weekly', 'Monthly'];

    return Container(
      padding: EdgeInsets.all(1.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: Row(
        children: List.generate(periods.length, (index) {
          final isSelected = index == _selectedPeriod;
          return GestureDetector(
            onTap: () => setState(() => _selectedPeriod = index),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
              decoration: BoxDecoration(
                color: isSelected
                    ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                periods[index],
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isSelected
                      ? Colors.white
                      : (isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight),
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildCashFlowMetrics(ThemeData theme, bool isDark) {
    final totalInflow = widget.financialPerformance['totalIncome'] ?? 0.0;
    final totalOutflow = widget.financialPerformance['totalExpenses'] ?? 0.0;
    final netCashFlow = totalInflow - totalOutflow;
    final avgDailyFlow = widget.financialPerformance['transactionsPerDay'] ?? 0.0;

    final metrics = [
      {
        'title': 'Total Inflow',
        'value': totalInflow,
        'icon': Icons.arrow_downward,
        'color': AppTheme.getSuccessColor(isDark),
        'subtitle': 'Money received',
      },
      {
        'title': 'Total Outflow',
        'value': totalOutflow,
        'icon': Icons.arrow_upward,
        'color': AppTheme.getErrorColor(isDark),
        'subtitle': 'Money spent',
      },
      {
        'title': 'Net Cash Flow',
        'value': netCashFlow,
        'icon': netCashFlow >= 0 ? Icons.trending_up : Icons.trending_down,
        'color': netCashFlow >= 0 ? AppTheme.getSuccessColor(isDark) : AppTheme.getErrorColor(isDark),
        'subtitle': 'Overall flow',
      },
      {
        'title': 'Daily Average',
        'value': avgDailyFlow * 100, // Approximate daily amount
        'icon': Icons.calendar_today,
        'color': isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
        'subtitle': 'Per day activity',
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 3.w,
        mainAxisSpacing: 2.h,
        childAspectRatio: 2.2,
      ),
      itemCount: metrics.length,
      itemBuilder: (context, index) {
        final metric = metrics[index];
        return Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: (metric['color'] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: (metric['color'] as Color).withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Icon(
                    metric['icon'] as IconData,
                    color: metric['color'] as Color,
                    size: 20,
                  ),
                  Container(
                    padding: EdgeInsets.all(1.w),
                    decoration: BoxDecoration(
                      color: (metric['color'] as Color).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.show_chart,
                      color: metric['color'] as Color,
                      size: 12,
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    metric['title'] as String,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    '\$${(metric['value'] as double).toStringAsFixed(0)}',
                    style: AppTheme.currencyStyle(
                      isLight: !isDark,
                      fontSize: 14.sp,
                    ),
                  ),
                  Text(
                    metric['subtitle'] as String,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCashFlowChart(ThemeData theme, bool isDark) {
    return Container(
      height: 35.h,
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Cash Flow Trend',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Row(
                children: [
                  _buildLegendItem('Inflow', AppTheme.getSuccessColor(isDark), theme),
                  SizedBox(width: 3.w),
                  _buildLegendItem('Outflow', AppTheme.getErrorColor(isDark), theme),
                  SizedBox(width: 3.w),
                  _buildLegendItem('Balance', isDark ? AppTheme.primaryDark : AppTheme.primaryLight, theme),
                ],
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Expanded(
            child: _buildCashFlowLineChart(theme, isDark),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color, ThemeData theme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 3.w,
          height: 3.w,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 1.w),
        Text(
          label,
          style: theme.textTheme.labelSmall,
        ),
      ],
    );
  }

  Widget _buildCashFlowLineChart(ThemeData theme, bool isDark) {
    if (widget.cashFlowData.isEmpty) {
      return Center(
        child: Text(
          'No cash flow data available',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
          ),
        ),
      );
    }

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: _calculateInterval(),
          getDrawingHorizontalLine: (value) => FlLine(
            color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
            strokeWidth: 1,
          ),
        ),
        titlesData: FlTitlesData(
          show: true,
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < widget.cashFlowData.length) {
                  final date = widget.cashFlowData[value.toInt()]['dateTime'] as DateTime;
                  return Text(
                    '${date.day}/${date.month}',
                    style: theme.textTheme.bodySmall,
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  '\$${(value / 1000).toStringAsFixed(0)}k',
                  style: theme.textTheme.bodySmall,
                );
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          // Inflow line
          LineChartBarData(
            spots: widget.cashFlowData.asMap().entries.map((entry) {
              return FlSpot(entry.key.toDouble(), entry.value['income'].toDouble());
            }).toList(),
            isCurved: true,
            color: AppTheme.getSuccessColor(isDark),
            barWidth: 2,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: false),
          ),
          // Outflow line
          LineChartBarData(
            spots: widget.cashFlowData.asMap().entries.map((entry) {
              return FlSpot(entry.key.toDouble(), entry.value['expense'].toDouble());
            }).toList(),
            isCurved: true,
            color: AppTheme.getErrorColor(isDark),
            barWidth: 2,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: false),
          ),
          // Balance line
          LineChartBarData(
            spots: widget.cashFlowData.asMap().entries.map((entry) {
              return FlSpot(entry.key.toDouble(), entry.value['balance'].toDouble());
            }).toList(),
            isCurved: true,
            color: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: (isDark ? AppTheme.primaryDark : AppTheme.primaryLight).withValues(alpha: 0.1),
            ),
          ),
        ],
        minX: 0,
        maxX: (widget.cashFlowData.length - 1).toDouble(),
        minY: _getMinValue(),
        maxY: _getMaxValue() * 1.1,
      ),
    );
  }

  Widget _buildLiquidityAnalysis(ThemeData theme, bool isDark) {
    final currentBalance = widget.cashFlowData.isNotEmpty 
        ? widget.cashFlowData.last['balance'] as double
        : 0.0;
    
    final avgDailyExpense = widget.financialPerformance['avgExpenseTransaction'] ?? 0.0;
    final daysOfLiquidity = avgDailyExpense > 0 ? currentBalance / avgDailyExpense : 0.0;
    
    final liquidityStatus = daysOfLiquidity > 30 
        ? 'Excellent' 
        : daysOfLiquidity > 15 
            ? 'Good' 
            : daysOfLiquidity > 7 
                ? 'Fair' 
                : 'Low';
    
    final statusColor = daysOfLiquidity > 30 
        ? AppTheme.getSuccessColor(isDark)
        : daysOfLiquidity > 15 
            ? AppTheme.getWarningColor(isDark)
            : AppTheme.getErrorColor(isDark);

    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Liquidity Analysis',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2.h),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Balance',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
                      ),
                    ),
                    Text(
                      '\$${currentBalance.toStringAsFixed(2)}',
                      style: AppTheme.currencyStyle(
                        isLight: !isDark,
                        fontSize: 16.sp,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Days of Liquidity',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
                      ),
                    ),
                    Text(
                      '${daysOfLiquidity.toStringAsFixed(0)} days',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: statusColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: statusColor.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  liquidityStatus,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  double _getMaxValue() {
    if (widget.cashFlowData.isEmpty) return 1000;
    
    double max = 0;
    for (final data in widget.cashFlowData) {
      final income = data['income'] as double;
      final expense = data['expense'] as double;
      final balance = data['balance'] as double;
      if (income > max) max = income;
      if (expense > max) max = expense;
      if (balance > max) max = balance;
    }
    return max;
  }

  double _getMinValue() {
    if (widget.cashFlowData.isEmpty) return 0;
    
    double min = 0;
    for (final data in widget.cashFlowData) {
      final balance = data['balance'] as double;
      if (balance < min) min = balance;
    }
    return min;
  }

  double _calculateInterval() {
    final maxValue = _getMaxValue();
    return maxValue / 5;
  }
}
