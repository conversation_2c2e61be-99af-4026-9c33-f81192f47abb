import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import './widgets/category_analysis_widget.dart';
import './widgets/chart_section_widget.dart';
import './widgets/date_range_selector_widget.dart';
import './widgets/export_bottom_sheet_widget.dart';
import './widgets/payment_mode_analytics_widget.dart';
import './widgets/summary_card_widget.dart';
import './widgets/income_expense_trends_widget.dart';
import './widgets/cash_flow_analysis_widget.dart';
import './widgets/financial_performance_widget.dart';
import './widgets/enhanced_category_breakdown_widget.dart';

class Reports extends StatefulWidget {
  const Reports({super.key});

  @override
  State<Reports> createState() => _ReportsState();
}

class _ReportsState extends State<Reports> with TickerProviderStateMixin {
  int _selectedDateRange = 1; // 0: Today, 1: Week, 2: Month, 3: Custom
  int _selectedReportType =
      0; // 0: Overview, 1: Trends, 2: Categories, 3: Payment, 4: Cash Flow, 5: Performance
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 7));
  DateTime _endDate = DateTime.now();

  // Data
  Map<String, dynamic>? _financialSummary;
  Map<String, dynamic>? _financialPerformance;
  List<Map<String, dynamic>> _trendsData = [];
  List<Map<String, dynamic>> _categoryData = [];
  List<Map<String, dynamic>> _paymentModeData = [];
  List<Map<String, dynamic>> _cashFlowData = [];
  bool _isLoading = true;
  String? _error;
  String? _selectedCashbookId;

  // Services
  late ReportsService _reportsService;

  @override
  void initState() {
    super.initState();
    _reportsService = ReportsService.instance;
    _loadReportsData();
  }

  Future<void> _loadReportsData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final results = await Future.wait([
        _reportsService.getFinancialSummary(
          cashbookId: _selectedCashbookId,
          startDate: _startDate,
          endDate: _endDate,
        ),
        _reportsService.getMonthlyTrends(
          cashbookId: _selectedCashbookId,
          months: 6,
        ),
        _reportsService.getCategoryAnalysis(
          cashbookId: _selectedCashbookId,
          startDate: _startDate,
          endDate: _endDate,
          type: TransactionType.expense,
        ),
        _reportsService.getPaymentModeAnalysis(
          cashbookId: _selectedCashbookId,
          startDate: _startDate,
          endDate: _endDate,
        ),
        _reportsService.getDailyCashFlow(
          cashbookId: _selectedCashbookId,
          startDate: _startDate,
          endDate: _endDate,
        ),
        _reportsService.getFinancialPerformance(
          cashbookId: _selectedCashbookId,
          startDate: _startDate,
          endDate: _endDate,
        ),
      ]);

      setState(() {
        _financialSummary = results[0] as Map<String, dynamic>;
        _trendsData = results[1] as List<Map<String, dynamic>>;
        _categoryData = results[2] as List<Map<String, dynamic>>;
        _paymentModeData = results[3] as List<Map<String, dynamic>>;
        _cashFlowData = results[4] as List<Map<String, dynamic>>;
        _financialPerformance = results[5] as Map<String, dynamic>;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _error = error.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Reports',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showExportBottomSheet,
            icon: CustomIconWidget(
              iconName: 'share',
              color:
                  isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
              size: 24,
            ),
            tooltip: 'Export Report',
          ),
          IconButton(
            onPressed: () {
              // Handle filter options
              _showFilterOptions();
            },
            icon: CustomIconWidget(
              iconName: 'filter_list',
              color:
                  isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
              size: 24,
            ),
            tooltip: 'Filter',
          ),
        ],
      ),
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _error != null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: theme.colorScheme.error,
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          'Failed to load reports',
                          style: theme.textTheme.headlineSmall,
                        ),
                        SizedBox(height: 1.h),
                        Text(
                          _error!,
                          style: theme.textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 3.h),
                        ElevatedButton(
                          onPressed: _loadReportsData,
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  )
                : Column(
                    children: [
                      // Date Range Selector
                      DateRangeSelectorWidget(
                        selectedIndex: _selectedDateRange,
                        onSelectionChanged: (index) {
                          setState(() {
                            _selectedDateRange = index;
                            _updateDateRange();
                          });
                        },
                        onCustomDateTap: _showCustomDatePicker,
                      ),

                      // Report Type Selector
                      _buildReportTypeSelector(theme, isDark),

                      // Scrollable Content
                      Expanded(
                        child: RefreshIndicator(
                          onRefresh: _loadReportsData,
                          child: SingleChildScrollView(
                            padding: EdgeInsets.all(4.w),
                            child: _buildSelectedReport(theme, isDark),
                          ),
                        ),
                      ),
                    ],
                  ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.pushNamed(context, '/add-transaction');
        },
        icon: CustomIconWidget(
          iconName: 'add',
          color: Colors.white,
          size: 20,
        ),
        label: Text(
          'Add Transaction',
          style: theme.textTheme.labelMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
      ),
    );
  }

  Widget _buildReportTypeSelector(ThemeData theme, bool isDark) {
    final reportTypes = [
      {'name': 'Overview', 'icon': Icons.dashboard},
      {'name': 'Trends', 'icon': Icons.trending_up},
      {'name': 'Categories', 'icon': Icons.pie_chart},
      {'name': 'Payments', 'icon': Icons.payment},
      {'name': 'Cash Flow', 'icon': Icons.waterfall_chart},
      {'name': 'Performance', 'icon': Icons.analytics},
    ];

    return Container(
      height: 8.h,
      padding: EdgeInsets.symmetric(vertical: 1.h),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 4.w),
        itemCount: reportTypes.length,
        itemBuilder: (context, index) {
          final isSelected = index == _selectedReportType;
          final reportType = reportTypes[index];

          return GestureDetector(
            onTap: () => setState(() => _selectedReportType = index),
            child: Container(
              margin: EdgeInsets.only(right: 3.w),
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
              decoration: BoxDecoration(
                color: isSelected
                    ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                    : (isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected
                      ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                      : (isDark ? AppTheme.dividerDark : AppTheme.dividerLight),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    reportType['icon'] as IconData,
                    color: isSelected
                        ? Colors.white
                        : (isDark
                            ? AppTheme.textSecondaryDark
                            : AppTheme.textSecondaryLight),
                    size: 20,
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    reportType['name'] as String,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isSelected
                          ? Colors.white
                          : (isDark
                              ? AppTheme.textSecondaryDark
                              : AppTheme.textSecondaryLight),
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSelectedReport(ThemeData theme, bool isDark) {
    switch (_selectedReportType) {
      case 0: // Overview
        return _buildOverviewReport(theme, isDark);
      case 1: // Trends
        return IncomeExpenseTrendsWidget(
          trendsData: _trendsData,
          financialSummary: _financialSummary ?? {},
        );
      case 2: // Categories
        return EnhancedCategoryBreakdownWidget(
          categoryData: _categoryData,
          trendsData: _trendsData,
        );
      case 3: // Payments
        return PaymentModeAnalyticsWidget(paymentModeData: _paymentModeData);
      case 4: // Cash Flow
        return CashFlowAnalysisWidget(
          cashFlowData: _cashFlowData,
          financialPerformance: _financialPerformance ?? {},
        );
      case 5: // Performance
        return FinancialPerformanceWidget(
          performanceData: _financialPerformance ?? {},
          financialSummary: _financialSummary ?? {},
        );
      default:
        return _buildOverviewReport(theme, isDark);
    }
  }

  Widget _buildOverviewReport(ThemeData theme, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Summary Cards
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: _buildSummaryCards(),
        ),

        SizedBox(height: 4.h),

        // Charts Section
        ChartSectionWidget(
          selectedChartType: 0,
          onChartTypeChanged: (index) {},
          chartData: _trendsData,
        ),

        SizedBox(height: 4.h),

        // Category Analysis
        CategoryAnalysisWidget(categoryData: _categoryData),

        SizedBox(height: 4.h),

        // Payment Mode Analytics
        PaymentModeAnalyticsWidget(paymentModeData: _paymentModeData),

        SizedBox(height: 4.h),

        // Additional Insights
        _buildInsightsSection(theme, isDark),

        SizedBox(height: 2.h),
      ],
    );
  }

  List<Widget> _buildSummaryCards() {
    if (_financialSummary == null) {
      return [
        SummaryCardWidget(
          title: "Total Income",
          amount: "\$0.00",
          percentage: "0.0%",
          isPositive: true,
          cardColor: const Color(0xFF4CAF50),
          icon: Icons.trending_up,
        ),
        SummaryCardWidget(
          title: "Total Expenses",
          amount: "\$0.00",
          percentage: "0.0%",
          isPositive: true,
          cardColor: const Color(0xFFF44336),
          icon: Icons.trending_down,
        ),
        SummaryCardWidget(
          title: "Net Profit",
          amount: "\$0.00",
          percentage: "0.0%",
          isPositive: true,
          cardColor: const Color(0xFF2196F3),
          icon: Icons.account_balance_wallet,
        ),
      ];
    }

    final summary = _financialSummary!;
    return [
      SummaryCardWidget(
        title: "Total Income",
        amount: "\$${summary['totalIncome'].toStringAsFixed(2)}",
        percentage: "${summary['incomeChange'].toStringAsFixed(1)}%",
        isPositive: summary['incomeChange'] >= 0,
        cardColor: const Color(0xFF4CAF50),
        icon: Icons.trending_up,
      ),
      SummaryCardWidget(
        title: "Total Expenses",
        amount: "\$${summary['totalExpenses'].toStringAsFixed(2)}",
        percentage: "${summary['expensesChange'].toStringAsFixed(1)}%",
        isPositive: summary['expensesChange'] <= 0, // Lower expenses are good
        cardColor: const Color(0xFFF44336),
        icon: Icons.trending_down,
      ),
      SummaryCardWidget(
        title: "Net Profit",
        amount: "\$${summary['netProfit'].toStringAsFixed(2)}",
        percentage: "${summary['profitChange'].toStringAsFixed(1)}%",
        isPositive: summary['profitChange'] >= 0,
        cardColor: const Color(0xFF2196F3),
        icon: Icons.account_balance_wallet,
      ),
    ];
  }

  Widget _buildInsightsSection(ThemeData theme, bool isDark) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomIconWidget(
                iconName: 'lightbulb_outline',
                color: isDark ? AppTheme.warningDark : AppTheme.warningLight,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'Financial Insights',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          _buildInsightItem(
            'Your food expenses increased by 7.5% this month',
            'Consider setting a monthly food budget to control spending',
            Icons.restaurant,
            AppTheme.getWarningColor(isDark),
            theme,
            isDark,
          ),
          SizedBox(height: 2.h),
          _buildInsightItem(
            'You saved \$320 on transportation this month',
            'Great job! Keep using public transport or carpooling',
            Icons.directions_bus,
            AppTheme.getSuccessColor(isDark),
            theme,
            isDark,
          ),
          SizedBox(height: 2.h),
          _buildInsightItem(
            'Your income is 15% higher than last month',
            'Consider increasing your savings rate to 20%',
            Icons.trending_up,
            AppTheme.getSuccessColor(isDark),
            theme,
            isDark,
          ),
        ],
      ),
    );
  }

  Widget _buildInsightItem(
    String title,
    String description,
    IconData icon,
    Color color,
    ThemeData theme,
    bool isDark,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomIconWidget(
            iconName: icon.codePoint.toString(),
            color: color,
            size: 16,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isDark
                      ? AppTheme.textSecondaryDark
                      : AppTheme.textSecondaryLight,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _updateDateRange() {
    final now = DateTime.now();
    switch (_selectedDateRange) {
      case 0: // Today
        _startDate = DateTime(now.year, now.month, now.day);
        _endDate = now;
        break;
      case 1: // Week
        _startDate = now.subtract(const Duration(days: 7));
        _endDate = now;
        break;
      case 2: // Month
        _startDate = DateTime(now.year, now.month - 1, now.day);
        _endDate = now;
        break;
      case 3: // Custom - handled by date picker
        break;
    }
    _loadReportsData(); // Reload data when date range changes
  }

  void _showCustomDatePicker() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).brightness == Brightness.dark
                      ? AppTheme.primaryDark
                      : AppTheme.primaryLight,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
        _selectedDateRange = 3;
      });
      _loadReportsData(); // Reload data when custom date range is selected
    }
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 12.w,
                height: 0.5.h,
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? AppTheme.dividerDark
                      : AppTheme.dividerLight,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            SizedBox(height: 3.h),
            Text(
              'Filter Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            SizedBox(height: 3.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'category',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
              title: const Text('Filter by Category'),
              trailing: CustomIconWidget(
                iconName: 'arrow_forward_ios',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textSecondaryDark
                    : AppTheme.textSecondaryLight,
                size: 16,
              ),
              onTap: () {
                Navigator.pop(context);
                // Handle category filter
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'payment',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
              title: const Text('Filter by Payment Mode'),
              trailing: CustomIconWidget(
                iconName: 'arrow_forward_ios',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textSecondaryDark
                    : AppTheme.textSecondaryLight,
                size: 16,
              ),
              onTap: () {
                Navigator.pop(context);
                // Handle payment mode filter
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'account_balance_wallet',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textPrimaryDark
                    : AppTheme.textPrimaryLight,
                size: 24,
              ),
              title: const Text('Filter by Cashbook'),
              trailing: CustomIconWidget(
                iconName: 'arrow_forward_ios',
                color: Theme.of(context).brightness == Brightness.dark
                    ? AppTheme.textSecondaryDark
                    : AppTheme.textSecondaryLight,
                size: 16,
              ),
              onTap: () {
                Navigator.pop(context);
                // Handle cashbook filter
              },
            ),
            SizedBox(height: 4.h),
          ],
        ),
      ),
    );
  }

  void _showExportBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ExportBottomSheetWidget(
        onExportSelected: _handleExport,
      ),
    );
  }

  Future<void> _handleExport(String format) async {
    try {
      String filePath;
      String message;

      switch (format) {
        case 'pdf':
          filePath = await _reportsService.exportToPDF(
            cashbookId: _selectedCashbookId,
            startDate: _startDate,
            endDate: _endDate,
            reportType: 'summary',
          );
          message = 'PDF report generated successfully';
          break;
        case 'csv':
          filePath = await _reportsService.exportToCSV(
            cashbookId: _selectedCashbookId,
            startDate: _startDate,
            endDate: _endDate,
            reportType: 'transactions',
          );
          message = 'CSV file exported successfully';
          break;
        default:
          throw Exception('Unsupported export format');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            action: SnackBarAction(
              label: 'Share',
              onPressed: () async {
                await _reportsService.shareFile(filePath, 'Cashbook Report');
              },
            ),
          ),
        );
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
