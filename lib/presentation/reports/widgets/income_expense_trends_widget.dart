import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class IncomeExpenseTrendsWidget extends StatefulWidget {
  final List<Map<String, dynamic>> trendsData;
  final Map<String, dynamic> financialSummary;

  const IncomeExpenseTrendsWidget({
    super.key,
    required this.trendsData,
    required this.financialSummary,
  });

  @override
  State<IncomeExpenseTrendsWidget> createState() => _IncomeExpenseTrendsWidgetState();
}

class _IncomeExpenseTrendsWidgetState extends State<IncomeExpenseTrendsWidget> {
  int _selectedChartType = 0; // 0: Line, 1: Bar, 2: Area

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme, isDark),
          SizedBox(height: 2.h),
          _buildSummaryCards(theme, isDark),
          SizedBox(height: 3.h),
          _buildChartSection(theme, isDark),
          SizedBox(height: 3.h),
          _buildTrendAnalysis(theme, isDark),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDark) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Income vs Expense Trends',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w700,
                color: isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
              ),
            ),
            SizedBox(height: 0.5.h),
            Text(
              'Financial performance over time',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
              ),
            ),
          ],
        ),
        _buildChartTypeSelector(theme, isDark),
      ],
    );
  }

  Widget _buildChartTypeSelector(ThemeData theme, bool isDark) {
    final chartTypes = [
      {'icon': Icons.show_chart, 'label': 'Line'},
      {'icon': Icons.bar_chart, 'label': 'Bar'},
      {'icon': Icons.area_chart, 'label': 'Area'},
    ];

    return Container(
      padding: EdgeInsets.all(1.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: Row(
        children: List.generate(chartTypes.length, (index) {
          final isSelected = index == _selectedChartType;
          return GestureDetector(
            onTap: () => setState(() => _selectedChartType = index),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
              decoration: BoxDecoration(
                color: isSelected
                    ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    chartTypes[index]['icon'] as IconData,
                    size: 16,
                    color: isSelected
                        ? Colors.white
                        : (isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight),
                  ),
                  SizedBox(width: 1.w),
                  Text(
                    chartTypes[index]['label'] as String,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isSelected
                          ? Colors.white
                          : (isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight),
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildSummaryCards(ThemeData theme, bool isDark) {
    final totalIncome = widget.financialSummary['totalIncome'] ?? 0.0;
    final totalExpenses = widget.financialSummary['totalExpenses'] ?? 0.0;
    final netProfit = widget.financialSummary['netProfit'] ?? 0.0;
    final incomeChange = widget.financialSummary['incomeChange'] ?? 0.0;
    final expensesChange = widget.financialSummary['expensesChange'] ?? 0.0;
    final profitChange = widget.financialSummary['profitChange'] ?? 0.0;

    final cards = [
      {
        'title': 'Total Income',
        'amount': totalIncome,
        'change': incomeChange,
        'color': AppTheme.getSuccessColor(isDark),
        'icon': Icons.trending_up,
      },
      {
        'title': 'Total Expenses',
        'amount': totalExpenses,
        'change': expensesChange,
        'color': AppTheme.getErrorColor(isDark),
        'icon': Icons.trending_down,
      },
      {
        'title': 'Net Profit',
        'amount': netProfit,
        'change': profitChange,
        'color': netProfit >= 0 ? AppTheme.getSuccessColor(isDark) : AppTheme.getErrorColor(isDark),
        'icon': Icons.account_balance_wallet,
      },
    ];

    return Row(
      children: cards.map((card) {
        return Expanded(
          child: Container(
            margin: EdgeInsets.only(right: cards.indexOf(card) < cards.length - 1 ? 3.w : 0),
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: (card['color'] as Color).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: (card['color'] as Color).withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Icon(
                      card['icon'] as IconData,
                      color: card['color'] as Color,
                      size: 20,
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                      decoration: BoxDecoration(
                        color: (card['change'] as double) >= 0
                            ? AppTheme.getSuccessColor(isDark).withValues(alpha: 0.1)
                            : AppTheme.getErrorColor(isDark).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        '${(card['change'] as double) >= 0 ? '+' : ''}${(card['change'] as double).toStringAsFixed(1)}%',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: (card['change'] as double) >= 0
                              ? AppTheme.getSuccessColor(isDark)
                              : AppTheme.getErrorColor(isDark),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 1.h),
                Text(
                  card['title'] as String,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  '\$${(card['amount'] as double).toStringAsFixed(0)}',
                  style: AppTheme.currencyStyle(
                    isLight: !isDark,
                    fontSize: 14.sp,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildChartSection(ThemeData theme, bool isDark) {
    return Container(
      height: 35.h,
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: _buildChart(theme, isDark),
    );
  }

  Widget _buildChart(ThemeData theme, bool isDark) {
    switch (_selectedChartType) {
      case 0:
        return _buildLineChart(theme, isDark);
      case 1:
        return _buildBarChart(theme, isDark);
      case 2:
        return _buildAreaChart(theme, isDark);
      default:
        return _buildLineChart(theme, isDark);
    }
  }

  Widget _buildLineChart(ThemeData theme, bool isDark) {
    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: _calculateInterval(),
          getDrawingHorizontalLine: (value) => FlLine(
            color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
            strokeWidth: 1,
          ),
        ),
        titlesData: _buildTitlesData(theme, isDark),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          _buildIncomeLineData(isDark),
          _buildExpenseLineData(isDark),
        ],
        minX: 0,
        maxX: (widget.trendsData.length - 1).toDouble(),
        minY: 0,
        maxY: _getMaxValue() * 1.1,
      ),
    );
  }

  Widget _buildTrendAnalysis(ThemeData theme, bool isDark) {
    if (widget.trendsData.length < 2) return const SizedBox.shrink();

    final latestData = widget.trendsData.last;
    final previousData = widget.trendsData[widget.trendsData.length - 2];
    
    final incomeGrowth = ((latestData['income'] - previousData['income']) / previousData['income'] * 100);
    final expenseGrowth = ((latestData['expense'] - previousData['expense']) / previousData['expense'] * 100);

    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Trend Analysis',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2.h),
          Row(
            children: [
              Expanded(
                child: _buildTrendItem(
                  'Income Growth',
                  incomeGrowth,
                  AppTheme.getSuccessColor(isDark),
                  theme,
                  isDark,
                ),
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: _buildTrendItem(
                  'Expense Growth',
                  expenseGrowth,
                  AppTheme.getErrorColor(isDark),
                  theme,
                  isDark,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTrendItem(String title, double value, Color color, ThemeData theme, bool isDark) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            value >= 0 ? Icons.trending_up : Icons.trending_down,
            color: color,
            size: 16,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isDark ? AppTheme.textSecondaryDark : AppTheme.textSecondaryLight,
                ),
              ),
              Text(
                '${value >= 0 ? '+' : ''}${value.toStringAsFixed(1)}%',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper methods for chart building
  LineChartBarData _buildIncomeLineData(bool isDark) {
    return LineChartBarData(
      spots: widget.trendsData.asMap().entries.map((entry) {
        return FlSpot(entry.key.toDouble(), entry.value['income'].toDouble());
      }).toList(),
      isCurved: true,
      color: AppTheme.getSuccessColor(isDark),
      barWidth: 3,
      isStrokeCapRound: true,
      dotData: const FlDotData(show: true),
      belowBarData: BarAreaData(
        show: true,
        color: AppTheme.getSuccessColor(isDark).withValues(alpha: 0.1),
      ),
    );
  }

  LineChartBarData _buildExpenseLineData(bool isDark) {
    return LineChartBarData(
      spots: widget.trendsData.asMap().entries.map((entry) {
        return FlSpot(entry.key.toDouble(), entry.value['expense'].toDouble());
      }).toList(),
      isCurved: true,
      color: AppTheme.getErrorColor(isDark),
      barWidth: 3,
      isStrokeCapRound: true,
      dotData: const FlDotData(show: true),
      belowBarData: BarAreaData(
        show: true,
        color: AppTheme.getErrorColor(isDark).withValues(alpha: 0.1),
      ),
    );
  }

  double _getMaxValue() {
    double max = 0;
    for (final data in widget.trendsData) {
      final income = data['income'] as double;
      final expense = data['expense'] as double;
      if (income > max) max = income;
      if (expense > max) max = expense;
    }
    return max;
  }

  double _calculateInterval() {
    final maxValue = _getMaxValue();
    return maxValue / 5;
  }

  FlTitlesData _buildTitlesData(ThemeData theme, bool isDark) {
    return FlTitlesData(
      show: true,
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          getTitlesWidget: (value, meta) {
            if (value.toInt() >= 0 && value.toInt() < widget.trendsData.length) {
              return Text(
                widget.trendsData[value.toInt()]['month'] ?? '',
                style: theme.textTheme.bodySmall,
              );
            }
            return const Text('');
          },
        ),
      ),
      leftTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 40,
          getTitlesWidget: (value, meta) {
            return Text(
              '\$${(value / 1000).toStringAsFixed(0)}k',
              style: theme.textTheme.bodySmall,
            );
          },
        ),
      ),
      topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
    );
  }

  Widget _buildBarChart(ThemeData theme, bool isDark) {
    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: _getMaxValue() * 1.1,
        titlesData: _buildTitlesData(theme, isDark),
        borderData: FlBorderData(show: false),
        barGroups: widget.trendsData.asMap().entries.map((entry) {
          return BarChartGroupData(
            x: entry.key,
            barRods: [
              BarChartRodData(
                toY: entry.value['income'].toDouble(),
                color: AppTheme.getSuccessColor(isDark),
                width: 4.w,
                borderRadius: BorderRadius.circular(4),
              ),
              BarChartRodData(
                toY: entry.value['expense'].toDouble(),
                color: AppTheme.getErrorColor(isDark),
                width: 4.w,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildAreaChart(ThemeData theme, bool isDark) {
    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: _calculateInterval(),
          getDrawingHorizontalLine: (value) => FlLine(
            color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
            strokeWidth: 1,
          ),
        ),
        titlesData: _buildTitlesData(theme, isDark),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: widget.trendsData.asMap().entries.map((entry) {
              return FlSpot(entry.key.toDouble(), entry.value['income'].toDouble());
            }).toList(),
            isCurved: true,
            color: AppTheme.getSuccessColor(isDark),
            barWidth: 0,
            dotData: const FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: AppTheme.getSuccessColor(isDark).withValues(alpha: 0.3),
            ),
          ),
          LineChartBarData(
            spots: widget.trendsData.asMap().entries.map((entry) {
              return FlSpot(entry.key.toDouble(), entry.value['expense'].toDouble());
            }).toList(),
            isCurved: true,
            color: AppTheme.getErrorColor(isDark),
            barWidth: 0,
            dotData: const FlDotData(show: false),
            belowBarData: BarAreaData(
              show: true,
              color: AppTheme.getErrorColor(isDark).withValues(alpha: 0.3),
            ),
          ),
        ],
        minX: 0,
        maxX: (widget.trendsData.length - 1).toDouble(),
        minY: 0,
        maxY: _getMaxValue() * 1.1,
      ),
    );
  }
}
