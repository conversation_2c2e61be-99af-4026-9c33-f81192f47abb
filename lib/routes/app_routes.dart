import 'package:flutter/material.dart';
import '../core/models/models.dart';
import '../presentation/splash/splash_screen.dart';
import '../presentation/auth/login_screen.dart';
import '../presentation/cashbook_management/cashbook_management.dart';
import '../presentation/dashboard/dashboard.dart';
import '../presentation/transaction_history/transaction_history.dart';
import '../presentation/categories_management/categories_management.dart';
import '../presentation/add_transaction/add_transaction.dart';
import '../presentation/reports/reports.dart';
import '../presentation/settings/settings_screen.dart';
import '../presentation/settings/app_settings_screen.dart';
import '../presentation/settings/business_settings_screen.dart';
import '../presentation/settings/profile_screen.dart';
import '../presentation/settings/about_screen.dart';
import '../presentation/business_members/business_members_screen.dart';
import '../presentation/business_members/member_permissions_screen.dart';
import '../presentation/business_management/business_management_screen.dart';

class AppRoutes {
  static const String initial = '/';
  static const String login = '/login';
  static const String cashbookManagement = '/cashbook-management';
  static const String dashboard = '/dashboard';
  static const String transactionHistory = '/transaction-history';
  static const String categoriesManagement = '/categories-management';
  static const String addTransaction = '/add-transaction';
  static const String reports = '/reports';
  static const String settings = '/settings';
  static const String appSettings = '/app-settings';
  static const String businessSettings = '/business-settings';
  static const String profile = '/profile';
  static const String about = '/about';
  static const String help = '/help';
  static const String businessMembers = '/business-members';
  static const String memberPermissions = '/member-permissions';
  static const String businessManagement = '/business-management';

  static Map<String, WidgetBuilder> routes = {
    '/': (context) => const SplashScreen(),
    '/splash': (context) => const SplashScreen(),
    '/login': (context) => const LoginScreen(),
    '/cashbook-management': (context) => const CashbookManagement(),
    '/dashboard': (context) => const Dashboard(),
    '/transaction-history': (context) => const TransactionHistory(),
    '/categories-management': (context) => const CategoriesManagement(),
    '/add-transaction': (context) => const AddTransaction(),
    '/reports': (context) => const Reports(),
    '/settings': (context) => const SettingsScreen(),
    '/app-settings': (context) => const AppSettingsScreen(),
    '/business-settings': (context) {
      final args =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      return BusinessSettingsScreen(cashbook: args ?? {});
    },
    '/profile': (context) => const ProfileScreen(),
    '/about': (context) => const AboutScreen(),
    '/help': (context) =>
        const AboutScreen(), // Using AboutScreen as placeholder for help
    '/business-members': (context) {
      final args =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      return BusinessMembersScreen(
        businessId: args?['businessId'] ?? '',
        businessName: args?['businessName'] ?? '',
      );
    },
    '/member-permissions': (context) {
      final args =
          ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      return MemberPermissionsScreen(
        member: args?['member'] as BusinessMemberModel,
        businessId: args?['businessId'] as String,
      );
    },
    '/business-management': (context) => const BusinessManagementScreen(),
  };
}
