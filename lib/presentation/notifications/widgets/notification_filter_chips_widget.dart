import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class NotificationFilterChipsWidget extends StatelessWidget {
  final String selectedFilter;
  final Function(String) onFilterChanged;

  const NotificationFilterChipsWidget({
    super.key,
    required this.selectedFilter,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    final filters = [
      'All',
      'Unread',
      'Transactions',
      'Invitations',
      'Broadcasts',
      'Reminders',
    ];

    return Container(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 4.w),
        child: Row(
          children: filters.map((filter) {
            final isSelected = selectedFilter == filter;
            return Container(
              margin: EdgeInsets.only(right: 2.w),
              child: FilterChip(
                label: Text(
                  filter,
                  style: TextStyle(
                    color: isSelected
                        ? Colors.white
                        : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    fontSize: 12.sp,
                  ),
                ),
                selected: isSelected,
                onSelected: (_) => onFilterChanged(filter),
                backgroundColor: theme.colorScheme.surface,
                selectedColor: isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
                checkmarkColor: Colors.white,
                side: BorderSide(
                  color: isSelected
                      ? (isDark ? AppTheme.primaryDark : AppTheme.primaryLight)
                      : theme.colorScheme.outline.withValues(alpha: 0.3),
                ),
                elevation: isSelected ? 2 : 0,
                pressElevation: 4,
              ),
            );
          }).toList(),
        ),
      ),
    );
  }
}
