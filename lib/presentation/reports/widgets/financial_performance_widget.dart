import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class FinancialPerformanceWidget extends StatelessWidget {
  final Map<String, dynamic> performanceData;
  final Map<String, dynamic> financialSummary;

  const FinancialPerformanceWidget({
    super.key,
    required this.performanceData,
    required this.financialSummary,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 4),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme, isDark),
          SizedBox(height: 3.h),
          _buildKPICards(theme, isDark),
          SizedBox(height: 3.h),
          _buildFinancialHealthScore(theme, isDark),
          SizedBox(height: 3.h),
          _buildPerformanceMetrics(theme, isDark),
          SizedBox(height: 3.h),
          _buildRecommendations(theme, isDark),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Financial Performance Dashboard',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w700,
            color:
                isDark ? AppTheme.textPrimaryDark : AppTheme.textPrimaryLight,
          ),
        ),
        SizedBox(height: 0.5.h),
        Text(
          'Comprehensive overview of your financial health',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isDark
                ? AppTheme.textSecondaryDark
                : AppTheme.textSecondaryLight,
          ),
        ),
      ],
    );
  }

  Widget _buildKPICards(ThemeData theme, bool isDark) {
    final savingsRate = performanceData['savingsRate'] ?? 0.0;
    final profitMargin = performanceData['profitMargin'] ?? 0.0;
    final transactionsPerDay = performanceData['transactionsPerDay'] ?? 0.0;
    final avgIncomeTransaction = performanceData['avgIncomeTransaction'] ?? 0.0;

    final kpis = [
      {
        'title': 'Savings Rate',
        'value': '${savingsRate.toStringAsFixed(1)}%',
        'subtitle': 'Of total income',
        'icon': Icons.savings,
        'color': _getSavingsRateColor(savingsRate, isDark),
        'trend': savingsRate >= 20
            ? 'excellent'
            : savingsRate >= 10
                ? 'good'
                : 'needs improvement',
      },
      {
        'title': 'Profit Margin',
        'value': '${profitMargin.toStringAsFixed(1)}%',
        'subtitle': 'Income efficiency',
        'icon': Icons.trending_up,
        'color': profitMargin >= 0
            ? AppTheme.getSuccessColor(isDark)
            : AppTheme.getErrorColor(isDark),
        'trend': profitMargin >= 20
            ? 'excellent'
            : profitMargin >= 0
                ? 'positive'
                : 'negative',
      },
      {
        'title': 'Activity Level',
        'value': '${transactionsPerDay.toStringAsFixed(1)}',
        'subtitle': 'Transactions/day',
        'icon': Icons.timeline,
        'color': isDark ? AppTheme.primaryDark : AppTheme.primaryLight,
        'trend': transactionsPerDay >= 5
            ? 'high'
            : transactionsPerDay >= 2
                ? 'moderate'
                : 'low',
      },
      {
        'title': 'Avg Income',
        'value': '\$${avgIncomeTransaction.toStringAsFixed(0)}',
        'subtitle': 'Per transaction',
        'icon': Icons.attach_money,
        'color': AppTheme.getSuccessColor(isDark),
        'trend': avgIncomeTransaction >= 1000
            ? 'high'
            : avgIncomeTransaction >= 500
                ? 'moderate'
                : 'low',
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 3.w,
        mainAxisSpacing: 2.h,
        childAspectRatio: 1.8,
      ),
      itemCount: kpis.length,
      itemBuilder: (context, index) {
        final kpi = kpis[index];
        return Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: (kpi['color'] as Color).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: (kpi['color'] as Color).withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Icon(
                    kpi['icon'] as IconData,
                    color: kpi['color'] as Color,
                    size: 24,
                  ),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                    decoration: BoxDecoration(
                      color: (kpi['color'] as Color).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      kpi['trend'] as String,
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: kpi['color'] as Color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    kpi['title'] as String,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDark
                          ? AppTheme.textSecondaryDark
                          : AppTheme.textSecondaryLight,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    kpi['value'] as String,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: isDark
                          ? AppTheme.textPrimaryDark
                          : AppTheme.textPrimaryLight,
                    ),
                  ),
                  Text(
                    kpi['subtitle'] as String,
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: isDark
                          ? AppTheme.textSecondaryDark
                          : AppTheme.textSecondaryLight,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFinancialHealthScore(ThemeData theme, bool isDark) {
    final score = _calculateFinancialHealthScore();
    final scoreColor = _getScoreColor(score, isDark);
    final scoreLabel = _getScoreLabel(score);

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Financial Health Score',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2.h),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          score.toString(),
                          style: theme.textTheme.displaySmall?.copyWith(
                            color: scoreColor,
                            fontWeight: FontWeight.w800,
                          ),
                        ),
                        Text(
                          '/100',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: isDark
                                ? AppTheme.textSecondaryDark
                                : AppTheme.textSecondaryLight,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 1.h),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                      decoration: BoxDecoration(
                        color: scoreColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: scoreColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        scoreLabel,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: scoreColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                flex: 3,
                child: Column(
                  children: [
                    _buildScoreBar(
                        'Savings Rate',
                        performanceData['savingsRate'] ?? 0.0,
                        30,
                        theme,
                        isDark),
                    SizedBox(height: 1.h),
                    _buildScoreBar(
                        'Profit Margin',
                        (performanceData['profitMargin'] ?? 0.0).clamp(0, 100),
                        100,
                        theme,
                        isDark),
                    SizedBox(height: 1.h),
                    _buildScoreBar(
                        'Transaction Activity',
                        ((performanceData['transactionsPerDay'] ?? 0.0) * 10)
                            .clamp(0, 100),
                        100,
                        theme,
                        isDark),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildScoreBar(String label, double value, double maxValue,
      ThemeData theme, bool isDark) {
    final percentage = (value / maxValue).clamp(0.0, 1.0);
    final color = percentage >= 0.7
        ? AppTheme.getSuccessColor(isDark)
        : percentage >= 0.4
            ? AppTheme.getWarningColor(isDark)
            : AppTheme.getErrorColor(isDark);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: isDark
                    ? AppTheme.textSecondaryDark
                    : AppTheme.textSecondaryLight,
              ),
            ),
            Text(
              '${(percentage * 100).toStringAsFixed(0)}%',
              style: theme.textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 0.5.h),
        LinearProgressIndicator(
          value: percentage,
          backgroundColor:
              isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
          valueColor: AlwaysStoppedAnimation<Color>(color),
          minHeight: 0.8.h,
        ),
      ],
    );
  }

  Widget _buildPerformanceMetrics(ThemeData theme, bool isDark) {
    final totalIncome = performanceData['totalIncome'] ?? 0.0;
    final totalExpenses = performanceData['totalExpenses'] ?? 0.0;
    final transactionCount = performanceData['transactionCount'] ?? 0;
    final avgExpenseTransaction =
        performanceData['avgExpenseTransaction'] ?? 0.0;

    final metrics = [
      {
        'label': 'Total Income',
        'value': '\$${totalIncome.toStringAsFixed(0)}',
        'icon': Icons.arrow_upward
      },
      {
        'label': 'Total Expenses',
        'value': '\$${totalExpenses.toStringAsFixed(0)}',
        'icon': Icons.arrow_downward
      },
      {
        'label': 'Total Transactions',
        'value': transactionCount.toString(),
        'icon': Icons.receipt_long
      },
      {
        'label': 'Avg Expense',
        'value': '\$${avgExpenseTransaction.toStringAsFixed(0)}',
        'icon': Icons.payment
      },
    ];

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Performance Metrics',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 2.h),
          ...metrics.map((metric) => Container(
                margin: EdgeInsets.only(bottom: 2.h),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: (isDark
                                ? AppTheme.primaryDark
                                : AppTheme.primaryLight)
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        metric['icon'] as IconData,
                        color: isDark
                            ? AppTheme.primaryDark
                            : AppTheme.primaryLight,
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            metric['label'] as String,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            metric['value'] as String,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: isDark
                                  ? AppTheme.textSecondaryDark
                                  : AppTheme.textSecondaryLight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildRecommendations(ThemeData theme, bool isDark) {
    final recommendations = _generateRecommendations();

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.surfaceDark : AppTheme.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: isDark ? AppTheme.warningDark : AppTheme.warningLight,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'Financial Recommendations',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          ...recommendations.map((recommendation) => Container(
                margin: EdgeInsets.only(bottom: 2.h),
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color:
                      (recommendation['color'] as Color).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: (recommendation['color'] as Color)
                        .withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      recommendation['icon'] as IconData,
                      color: recommendation['color'] as Color,
                      size: 16,
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            recommendation['title'] as String,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: recommendation['color'] as Color,
                            ),
                          ),
                          SizedBox(height: 0.5.h),
                          Text(
                            recommendation['description'] as String,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: isDark
                                  ? AppTheme.textSecondaryDark
                                  : AppTheme.textSecondaryLight,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  // Helper methods
  Color _getSavingsRateColor(double rate, bool isDark) {
    if (rate >= 20) return AppTheme.getSuccessColor(isDark);
    if (rate >= 10) return AppTheme.getWarningColor(isDark);
    return AppTheme.getErrorColor(isDark);
  }

  int _calculateFinancialHealthScore() {
    final savingsRate = performanceData['savingsRate'] ?? 0.0;
    final profitMargin = performanceData['profitMargin'] ?? 0.0;
    final transactionsPerDay = performanceData['transactionsPerDay'] ?? 0.0;

    int score = 0;

    // Savings rate (40 points max)
    if (savingsRate >= 20) {
      score += 40;
    } else if (savingsRate >= 15) {
      score += 30;
    } else if (savingsRate >= 10) {
      score += 20;
    } else if (savingsRate >= 5) {
      score += 10;
    }

    // Profit margin (30 points max)
    if (profitMargin >= 20) {
      score += 30;
    } else if (profitMargin >= 10) {
      score += 20;
    } else if (profitMargin >= 0) {
      score += 10;
    }

    // Activity level (30 points max)
    if (transactionsPerDay >= 5) {
      score += 30;
    } else if (transactionsPerDay >= 3) {
      score += 20;
    } else if (transactionsPerDay >= 1) {
      score += 10;
    }

    return score.clamp(0, 100);
  }

  Color _getScoreColor(int score, bool isDark) {
    if (score >= 80) return AppTheme.getSuccessColor(isDark);
    if (score >= 60) return AppTheme.getWarningColor(isDark);
    return AppTheme.getErrorColor(isDark);
  }

  String _getScoreLabel(int score) {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Good';
    if (score >= 40) return 'Fair';
    return 'Needs Improvement';
  }

  List<Map<String, dynamic>> _generateRecommendations() {
    final recommendations = <Map<String, dynamic>>[];
    final savingsRate = performanceData['savingsRate'] ?? 0.0;
    final profitMargin = performanceData['profitMargin'] ?? 0.0;
    final transactionsPerDay = performanceData['transactionsPerDay'] ?? 0.0;

    if (savingsRate < 10) {
      recommendations.add({
        'title': 'Improve Savings Rate',
        'description':
            'Try to save at least 10-20% of your income. Consider automating your savings.',
        'icon': Icons.savings,
        'color': const Color(0xFFFF9800),
      });
    }

    if (profitMargin < 0) {
      recommendations.add({
        'title': 'Reduce Expenses',
        'description':
            'Your expenses exceed income. Review and cut unnecessary spending.',
        'icon': Icons.trending_down,
        'color': const Color(0xFFF44336),
      });
    }

    if (transactionsPerDay < 1) {
      recommendations.add({
        'title': 'Track More Transactions',
        'description':
            'Regular transaction tracking helps better financial management.',
        'icon': Icons.receipt_long,
        'color': const Color(0xFF2196F3),
      });
    }

    if (recommendations.isEmpty) {
      recommendations.add({
        'title': 'Great Financial Health!',
        'description': 'Keep up the good work with your financial management.',
        'icon': Icons.check_circle,
        'color': const Color(0xFF4CAF50),
      });
    }

    return recommendations;
  }
}
