import 'notification_model.dart';

class NotificationSettings {
  final String userId;
  final bool pushNotificationsEnabled;
  final bool localNotificationsEnabled;
  final bool transactionNotifications;
  final bool invitationNotifications;
  final bool broadcastNotifications;
  final bool reminderNotifications;
  final bool systemNotifications;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String? quietHoursStart;
  final String? quietHoursEnd;
  final DateTime createdAt;
  final DateTime updatedAt;

  NotificationSettings({
    required this.userId,
    this.pushNotificationsEnabled = true,
    this.localNotificationsEnabled = true,
    this.transactionNotifications = true,
    this.invitationNotifications = true,
    this.broadcastNotifications = true,
    this.reminderNotifications = true,
    this.systemNotifications = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.quietHoursStart,
    this.quietHoursEnd,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      userId: json['user_id'] as String,
      pushNotificationsEnabled:
          json['push_notifications_enabled'] as bool? ?? true,
      localNotificationsEnabled:
          json['local_notifications_enabled'] as bool? ?? true,
      transactionNotifications:
          json['transaction_notifications'] as bool? ?? true,
      invitationNotifications:
          json['invitation_notifications'] as bool? ?? true,
      broadcastNotifications: json['broadcast_notifications'] as bool? ?? true,
      reminderNotifications: json['reminder_notifications'] as bool? ?? true,
      systemNotifications: json['system_notifications'] as bool? ?? true,
      soundEnabled: json['sound_enabled'] as bool? ?? true,
      vibrationEnabled: json['vibration_enabled'] as bool? ?? true,
      quietHoursStart: json['quiet_hours_start'] as String?,
      quietHoursEnd: json['quiet_hours_end'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'push_notifications_enabled': pushNotificationsEnabled,
      'local_notifications_enabled': localNotificationsEnabled,
      'transaction_notifications': transactionNotifications,
      'invitation_notifications': invitationNotifications,
      'broadcast_notifications': broadcastNotifications,
      'reminder_notifications': reminderNotifications,
      'system_notifications': systemNotifications,
      'sound_enabled': soundEnabled,
      'vibration_enabled': vibrationEnabled,
      'quiet_hours_start': quietHoursStart,
      'quiet_hours_end': quietHoursEnd,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  NotificationSettings copyWith({
    String? userId,
    bool? pushNotificationsEnabled,
    bool? localNotificationsEnabled,
    bool? transactionNotifications,
    bool? invitationNotifications,
    bool? broadcastNotifications,
    bool? reminderNotifications,
    bool? systemNotifications,
    bool? soundEnabled,
    bool? vibrationEnabled,
    String? quietHoursStart,
    String? quietHoursEnd,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NotificationSettings(
      userId: userId ?? this.userId,
      pushNotificationsEnabled:
          pushNotificationsEnabled ?? this.pushNotificationsEnabled,
      localNotificationsEnabled:
          localNotificationsEnabled ?? this.localNotificationsEnabled,
      transactionNotifications:
          transactionNotifications ?? this.transactionNotifications,
      invitationNotifications:
          invitationNotifications ?? this.invitationNotifications,
      broadcastNotifications:
          broadcastNotifications ?? this.broadcastNotifications,
      reminderNotifications:
          reminderNotifications ?? this.reminderNotifications,
      systemNotifications: systemNotifications ?? this.systemNotifications,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      quietHoursStart: quietHoursStart ?? this.quietHoursStart,
      quietHoursEnd: quietHoursEnd ?? this.quietHoursEnd,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool isNotificationTypeEnabled(NotificationType type) {
    switch (type) {
      case NotificationType.transaction:
        return transactionNotifications;
      case NotificationType.invitation:
        return invitationNotifications;
      case NotificationType.broadcast:
        return broadcastNotifications;
      case NotificationType.reminder:
        return reminderNotifications;
      case NotificationType.system:
        return systemNotifications;
    }
  }

  bool get hasQuietHours => quietHoursStart != null && quietHoursEnd != null;

  bool isInQuietHours() {
    if (!hasQuietHours) return false;

    final now = DateTime.now();
    final currentTime =
        '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

    // Simple time comparison - could be enhanced for cross-midnight ranges
    return currentTime.compareTo(quietHoursStart!) >= 0 &&
        currentTime.compareTo(quietHoursEnd!) <= 0;
  }
}

class BroadcastNotificationRequest {
  final String businessId;
  final String title;
  final String message;
  final NotificationPriority priority;
  final List<String>? targetUserIds;
  final List<String>? targetCashbookIds;
  final Map<String, dynamic>? data;
  final String? actionUrl;

  BroadcastNotificationRequest({
    required this.businessId,
    required this.title,
    required this.message,
    this.priority = NotificationPriority.normal,
    this.targetUserIds,
    this.targetCashbookIds,
    this.data,
    this.actionUrl,
  });

  Map<String, dynamic> toJson() {
    return {
      'business_id': businessId,
      'title': title,
      'message': message,
      'priority': priority.name,
      'target_user_ids': targetUserIds,
      'target_cashbook_ids': targetCashbookIds,
      'data': data,
      'action_url': actionUrl,
    };
  }
}
