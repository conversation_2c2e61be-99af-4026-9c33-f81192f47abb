import '../models/models.dart';
import 'base_repository.dart';

class NotificationRepository extends BaseRepository {
  static const String _notificationsTable = 'notifications';
  static const String _notificationSettingsTable = 'notification_settings';

  /// Get notifications for current user
  Future<List<NotificationModel>> getUserNotifications({
    int limit = 50,
    int offset = 0,
    bool? isRead,
    NotificationType? type,
    String? businessId,
  }) async {
    requireAuth();

    return executeQuery(() async {
      var query = client
          .from(_notificationsTable)
          .select('''
            *,
            businesses!notifications_business_id_fkey(name),
            cashbooks!notifications_cashbook_id_fkey(name),
            users!notifications_user_id_fkey(full_name)
          ''')
          .eq('user_id', currentUserId!)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      // if (isRead != null) {
      //   query = query.eq('is_read', isRead);
      // }

      // if (type != null) {
      //   query = query.eq('type', type.name);
      // }

      // if (businessId != null) {
      //   query = query.eq('business_id', businessId);
      // }

      final response = await query;

      return response.map((json) {
        // Add related data to the notification
        final notificationData = Map<String, dynamic>.from(json);
        if (json['businesses'] != null) {
          notificationData['business_name'] = json['businesses']['name'];
        }
        if (json['cashbooks'] != null) {
          notificationData['cashbook_name'] = json['cashbooks']['name'];
        }
        if (json['users'] != null) {
          notificationData['sender_name'] = json['users']['full_name'];
        }

        return NotificationModel.fromJson(notificationData);
      }).toList();
    });
  }

  /// Get unread notification count
  Future<int> getUnreadCount() async {
    requireAuth();

    return executeQuery(() async {
      final response = await client
          .from(_notificationsTable)
          .select('id')
          .eq('user_id', currentUserId!)
          .eq('is_read', false)
          .count();

      return response.count;
    });
  }

  /// Mark notification as read
  Future<NotificationModel> markAsRead(String notificationId) async {
    requireAuth();

    return executeQuery(() async {
      final now = DateTime.now();
      final response = await client
          .from(_notificationsTable)
          .update({
            'is_read': true,
            'read_at': now.toIso8601String(),
            'updated_at': now.toIso8601String(),
          })
          .eq('id', notificationId)
          .eq('user_id', currentUserId!)
          .select()
          .single();

      return NotificationModel.fromJson(response);
    });
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    requireAuth();

    return executeQuery(() async {
      final now = DateTime.now();
      await client
          .from(_notificationsTable)
          .update({
            'is_read': true,
            'read_at': now.toIso8601String(),
            'updated_at': now.toIso8601String(),
          })
          .eq('user_id', currentUserId!)
          .eq('is_read', false);
    });
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    requireAuth();

    return executeQuery(() async {
      await client
          .from(_notificationsTable)
          .delete()
          .eq('id', notificationId)
          .eq('user_id', currentUserId!);
    });
  }

  /// Create notification
  Future<NotificationModel> createNotification({
    required String userId,
    String? businessId,
    String? cashbookId,
    required NotificationType type,
    NotificationPriority priority = NotificationPriority.normal,
    required String title,
    required String message,
    Map<String, dynamic>? data,
    String? actionUrl,
  }) async {
    requireAuth();

    return executeQuery(() async {
      final now = DateTime.now();
      final notificationData = {
        'user_id': userId,
        'business_id': businessId,
        'cashbook_id': cashbookId,
        'type': type.name,
        'priority': priority.name,
        'title': title,
        'message': message,
        'data': data,
        'action_url': actionUrl,
        'is_read': false,
        'is_delivered': false,
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      final response = await client
          .from(_notificationsTable)
          .insert(notificationData)
          .select()
          .single();

      return NotificationModel.fromJson(response);
    });
  }

  /// Broadcast notification to business members
  Future<List<NotificationModel>> broadcastNotification(
    BroadcastNotificationRequest request,
  ) async {
    requireAuth();

    return executeQuery(() async {
      // Get business members if no specific target users provided
      List<String> targetUsers = request.targetUserIds ?? [];

      if (targetUsers.isEmpty) {
        final membersResponse = await client
            .from('business_members')
            .select('user_id')
            .eq('business_id', request.businessId);

        targetUsers = membersResponse
            .map((member) => member['user_id'] as String)
            .toList();
      }

      // Create notifications for all target users
      final notifications = <NotificationModel>[];
      final now = DateTime.now();

      for (final userId in targetUsers) {
        final notificationData = {
          'user_id': userId,
          'business_id': request.businessId,
          'type': NotificationType.broadcast.name,
          'priority': request.priority.name,
          'title': request.title,
          'message': request.message,
          'data': request.data,
          'action_url': request.actionUrl,
          'is_read': false,
          'is_delivered': false,
          'created_at': now.toIso8601String(),
          'updated_at': now.toIso8601String(),
        };

        final response = await client
            .from(_notificationsTable)
            .insert(notificationData)
            .select()
            .single();

        notifications.add(NotificationModel.fromJson(response));
      }

      return notifications;
    });
  }

  /// Get notification settings for user
  Future<NotificationSettings?> getNotificationSettings() async {
    requireAuth();

    return executeQuery(() async {
      try {
        final response = await client
            .from(_notificationSettingsTable)
            .select()
            .eq('user_id', currentUserId!)
            .single();

        return NotificationSettings.fromJson(response);
      } catch (e) {
        // Return null if settings don't exist yet
        return null;
      }
    });
  }

  /// Update notification settings
  Future<NotificationSettings> updateNotificationSettings(
    NotificationSettings settings,
  ) async {
    requireAuth();

    return executeQuery(() async {
      final now = DateTime.now();
      final settingsData = settings.copyWith(updatedAt: now).toJson();

      final response = await client
          .from(_notificationSettingsTable)
          .upsert(settingsData)
          .select()
          .single();

      return NotificationSettings.fromJson(response);
    });
  }

  /// Create default notification settings for user
  Future<NotificationSettings> createDefaultSettings() async {
    requireAuth();

    return executeQuery(() async {
      final now = DateTime.now();
      final settings = NotificationSettings(
        userId: currentUserId!,
        createdAt: now,
        updatedAt: now,
      );

      final response = await client
          .from(_notificationSettingsTable)
          .insert(settings.toJson())
          .select()
          .single();

      return NotificationSettings.fromJson(response);
    });
  }
}
