import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class PaymentModeAnalyticsWidget extends StatelessWidget {
  final List<Map<String, dynamic>> paymentModeData;

  const PaymentModeAnalyticsWidget({
    super.key,
    required this.paymentModeData,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.black.withValues(alpha: 0.04),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Methods',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 3.h),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: _buildPaymentModeChart(theme, isDark),
              ),
              SizedBox(width: 4.w),
              Expanded(
                flex: 3,
                child: _buildPaymentModeLegend(theme, isDark),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentModeChart(ThemeData theme, bool isDark) {
    final totalAmount = paymentModeData.fold<double>(
      0.0,
      (sum, item) => sum + (item['amount'] as double),
    );

    return SizedBox(
      height: 20.h,
      child: Stack(
        children: [
          Center(
            child: SizedBox(
              width: 20.w,
              height: 20.w,
              child: CircularProgressIndicator(
                value: 1.0,
                strokeWidth: 8,
                backgroundColor:
                    isDark ? AppTheme.dividerDark : AppTheme.dividerLight,
                valueColor: AlwaysStoppedAnimation<Color>(
                  paymentModeData.isNotEmpty
                      ? (paymentModeData[0]['color'] as Color? ??
                          const Color(0xFF4CAF50))
                      : (isDark ? AppTheme.primaryDark : AppTheme.primaryLight),
                ),
              ),
            ),
          ),
          if (paymentModeData.length > 1)
            Center(
              child: SizedBox(
                width: 16.w,
                height: 16.w,
                child: CircularProgressIndicator(
                  value: paymentModeData.length > 1
                      ? (paymentModeData[1]['amount'] as double) / totalAmount
                      : 0.0,
                  strokeWidth: 6,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    paymentModeData.length > 1
                        ? (paymentModeData[1]['color'] as Color? ??
                            const Color(0xFF2196F3))
                        : Colors.transparent,
                  ),
                ),
              ),
            ),
          Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomIconWidget(
                  iconName: 'account_balance_wallet',
                  color: isDark
                      ? AppTheme.textSecondaryDark
                      : AppTheme.textSecondaryLight,
                  size: 24,
                ),
                SizedBox(height: 1.h),
                Text(
                  'Total',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isDark
                        ? AppTheme.textSecondaryDark
                        : AppTheme.textSecondaryLight,
                  ),
                ),
                Text(
                  '\$${totalAmount.toStringAsFixed(0)}',
                  style: AppTheme.currencyStyle(
                    isLight: !isDark,
                    fontSize: 12.sp,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentModeLegend(ThemeData theme, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: paymentModeData.map((mode) {
        final percentage = paymentModeData.fold<double>(
                    0.0, (sum, item) => sum + (item['amount'] as double)) >
                0
            ? (mode['amount'] as double) /
                paymentModeData.fold<double>(
                    0.0, (sum, item) => sum + (item['amount'] as double)) *
                100
            : 0.0;

        return Container(
          margin: EdgeInsets.only(bottom: 2.h),
          child: Row(
            children: [
              Container(
                width: 3.w,
                height: 3.w,
                decoration: BoxDecoration(
                  color: mode['color'] as Color? ?? const Color(0xFF9E9E9E),
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            CustomIconWidget(
                              iconName: mode['icon'] as String? ?? 'payment',
                              color: isDark
                                  ? AppTheme.textPrimaryDark
                                  : AppTheme.textPrimaryLight,
                              size: 16,
                            ),
                            SizedBox(width: 2.w),
                            Text(
                              mode['name'] as String? ??
                                  mode['paymentMode'] as String? ??
                                  'Unknown',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        Text(
                          '${percentage.toStringAsFixed(1)}%',
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      '\$${(mode['amount'] as double).toStringAsFixed(0)}',
                      style: AppTheme.currencyStyle(
                        isLight: !isDark,
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
