import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart' as firebase;
import 'package:supabase_flutter/supabase_flutter.dart';

import '../models/models.dart';
import '../repositories/repositories.dart';
import 'supabase_service.dart';
import 'preferences_service.dart';

class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance =>
      _instance ??= NotificationService._();

  NotificationService._();

  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  final firebase.FirebaseMessaging _firebaseMessaging =
      firebase.FirebaseMessaging.instance;
  late final NotificationRepository _repository;

  StreamSubscription<List<Map<String, dynamic>>>? _realtimeSubscription;
  final StreamController<NotificationModel> _notificationStreamController =
      StreamController<NotificationModel>.broadcast();
  final StreamController<int> _unreadCountStreamController =
      StreamController<int>.broadcast();

  // Getters for streams
  Stream<NotificationModel> get notificationStream =>
      _notificationStreamController.stream;
  Stream<int> get unreadCountStream => _unreadCountStreamController.stream;

  bool _isInitialized = false;
  NotificationSettings? _currentSettings;

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    _repository = NotificationRepository();

    await _initializeLocalNotifications();
    await _initializePushNotifications();
    await _loadNotificationSettings();
    await _setupRealtimeSubscription();

    _isInitialized = true;
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Request permissions for Android 13+
    if (Platform.isAndroid) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
    }
  }

  /// Initialize push notifications
  Future<void> _initializePushNotifications() async {
    // Request permission
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    if (settings.authorizationStatus ==
        firebase.AuthorizationStatus.authorized) {
      debugPrint('User granted permission for push notifications');

      // Get FCM token
      final token = await _firebaseMessaging.getToken();
      if (token != null) {
        await _saveFCMToken(token);
      }

      // Listen for token refresh
      _firebaseMessaging.onTokenRefresh.listen(_saveFCMToken);

      // Handle foreground messages
      firebase.FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle background messages
      firebase.FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

      // Handle notification taps when app is in background/terminated
      firebase.FirebaseMessaging.onMessageOpenedApp
          .listen(_handleNotificationTap);
    }
  }

  /// Load notification settings
  Future<void> _loadNotificationSettings() async {
    try {
      _currentSettings = await _repository.getNotificationSettings();
      _currentSettings ??= await _repository.createDefaultSettings();
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
      // Create default settings if loading fails
      _currentSettings = NotificationSettings(
        userId: SupabaseService.instance.currentUserId!,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }

  /// Setup real-time subscription for notifications
  Future<void> _setupRealtimeSubscription() async {
    final userId = SupabaseService.instance.currentUserId;
    if (userId == null) return;

    try {
      _realtimeSubscription = SupabaseService.instance.client
          .from('notifications')
          .stream(primaryKey: ['id'])
          .eq('user_id', userId)
          .listen((data) {
            for (final item in data) {
              final notification = NotificationModel.fromJson(item);
              _notificationStreamController.add(notification);

              // Show local notification if enabled
              if (_shouldShowLocalNotification(notification)) {
                _showLocalNotification(notification);
              }
            }

            // Update unread count
            _updateUnreadCount();
          });
    } catch (e) {
      debugPrint('Error setting up real-time subscription: $e');
    }
  }

  /// Save FCM token to user profile
  Future<void> _saveFCMToken(String token) async {
    try {
      final userId = SupabaseService.instance.currentUserId;
      if (userId != null) {
        await SupabaseService.instance.client
            .from('users')
            .update({'fcm_token': token}).eq('id', userId);
      }
    } catch (e) {
      debugPrint('Error saving FCM token: $e');
    }
  }

  /// Handle foreground push messages
  Future<void> _handleForegroundMessage(firebase.RemoteMessage message) async {
    debugPrint('Received foreground message: ${message.messageId}');

    // Create local notification from push message
    if (message.notification != null) {
      await _showLocalNotificationFromPush(message);
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');
    // TODO: Navigate to appropriate screen based on payload
  }

  /// Handle push notification tap
  void _handleNotificationTap(firebase.RemoteMessage message) {
    debugPrint('Push notification tapped: ${message.messageId}');
    // TODO: Navigate to appropriate screen based on message data
  }

  /// Show local notification
  Future<void> _showLocalNotification(NotificationModel notification) async {
    if (!_currentSettings!.localNotificationsEnabled) return;
    if (_currentSettings!.isInQuietHours()) return;

    const androidDetails = AndroidNotificationDetails(
      'cashbook_notifications',
      'Cashbook Notifications',
      channelDescription: 'Notifications for cashbook activities',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      notification.id.hashCode,
      notification.title,
      notification.message,
      details,
      payload: notification.actionUrl,
    );
  }

  /// Show local notification from push message
  Future<void> _showLocalNotificationFromPush(
      firebase.RemoteMessage message) async {
    const androidDetails = AndroidNotificationDetails(
      'cashbook_push',
      'Cashbook Push Notifications',
      channelDescription: 'Push notifications for cashbook activities',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'Cashbook',
      message.notification?.body ?? '',
      details,
      payload: message.data['action_url'],
    );
  }

  /// Check if local notification should be shown
  bool _shouldShowLocalNotification(NotificationModel notification) {
    if (!_currentSettings!.localNotificationsEnabled) return false;
    if (!_currentSettings!.isNotificationTypeEnabled(notification.type))
      return false;
    if (_currentSettings!.isInQuietHours()) return false;

    return true;
  }

  /// Update unread count
  Future<void> _updateUnreadCount() async {
    try {
      final count = await _repository.getUnreadCount();
      _unreadCountStreamController.add(count);
    } catch (e) {
      debugPrint('Error updating unread count: $e');
    }
  }

  /// Get notifications
  Future<List<NotificationModel>> getNotifications({
    int limit = 50,
    int offset = 0,
    bool? isRead,
    NotificationType? type,
    String? businessId,
  }) async {
    return await _repository.getUserNotifications(
      limit: limit,
      offset: offset,
      isRead: isRead,
      type: type,
      businessId: businessId,
    );
  }

  /// Get unread count
  Future<int> getUnreadCount() async {
    return await _repository.getUnreadCount();
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    await _repository.markAsRead(notificationId);
    await _updateUnreadCount();
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    await _repository.markAllAsRead();
    await _updateUnreadCount();
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    await _repository.deleteNotification(notificationId);
    await _updateUnreadCount();
  }

  /// Broadcast notification
  Future<void> broadcastNotification(
      BroadcastNotificationRequest request) async {
    await _repository.broadcastNotification(request);
  }

  /// Get notification settings
  NotificationSettings? get currentSettings => _currentSettings;

  /// Update notification settings
  Future<void> updateSettings(NotificationSettings settings) async {
    _currentSettings = await _repository.updateNotificationSettings(settings);
  }

  /// Dispose resources
  void dispose() {
    _realtimeSubscription?.cancel();
    _notificationStreamController.close();
    _unreadCountStreamController.close();
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _handleBackgroundMessage(firebase.RemoteMessage message) async {
  debugPrint('Handling background message: ${message.messageId}');
}
