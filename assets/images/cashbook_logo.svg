<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient for the book -->
    <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
    
    <!-- Gradient for the coin -->
    <linearGradient id="coinGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA000;stop-opacity:1" />
    </linearGradient>
    
    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="95" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
  
  <!-- Book base -->
  <rect x="40" y="60" width="80" height="100" rx="4" ry="4" fill="url(#bookGradient)" filter="url(#shadow)"/>
  
  <!-- Book spine -->
  <rect x="40" y="60" width="8" height="100" rx="4" ry="4" fill="#1B5E20"/>
  
  <!-- Book pages -->
  <rect x="48" y="65" width="68" height="90" rx="2" ry="2" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1"/>
  
  <!-- Page lines -->
  <line x1="55" y1="80" x2="105" y2="80" stroke="#BDBDBD" stroke-width="1"/>
  <line x1="55" y1="90" x2="105" y2="90" stroke="#BDBDBD" stroke-width="1"/>
  <line x1="55" y1="100" x2="105" y2="100" stroke="#BDBDBD" stroke-width="1"/>
  <line x1="55" y1="110" x2="105" y2="110" stroke="#BDBDBD" stroke-width="1"/>
  <line x1="55" y1="120" x2="105" y2="120" stroke="#BDBDBD" stroke-width="1"/>
  
  <!-- Dollar sign on the book -->
  <text x="82" y="140" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2E7D32" text-anchor="middle">$</text>
  
  <!-- Floating coin -->
  <circle cx="140" cy="50" r="20" fill="url(#coinGradient)" filter="url(#shadow)"/>
  <text x="140" y="58" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#B8860B" text-anchor="middle">$</text>
  
  <!-- Small coins -->
  <circle cx="160" cy="80" r="8" fill="url(#coinGradient)" opacity="0.8"/>
  <text x="160" y="85" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="#B8860B" text-anchor="middle">¢</text>
  
  <circle cx="150" cy="110" r="6" fill="url(#coinGradient)" opacity="0.6"/>
  <text x="150" y="114" font-family="Arial, sans-serif" font-size="6" font-weight="bold" fill="#B8860B" text-anchor="middle">¢</text>
  
  <!-- Pen/pencil -->
  <rect x="120" y="140" width="3" height="25" rx="1.5" ry="1.5" fill="#FF9800" transform="rotate(15 121.5 152.5)"/>
  <rect x="119" y="138" width="5" height="4" rx="2" ry="2" fill="#FFB74D" transform="rotate(15 121.5 140)"/>
</svg>
