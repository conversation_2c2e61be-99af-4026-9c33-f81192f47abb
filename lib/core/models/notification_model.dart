enum NotificationType {
  transaction,
  invitation,
  broadcast,
  reminder,
  system;

  String get displayName {
    switch (this) {
      case NotificationType.transaction:
        return 'Transaction';
      case NotificationType.invitation:
        return 'Invitation';
      case NotificationType.broadcast:
        return 'Broadcast';
      case NotificationType.reminder:
        return 'Reminder';
      case NotificationType.system:
        return 'System';
    }
  }

  static NotificationType fromString(String value) {
    return NotificationType.values.firstWhere(
      (type) => type.name == value,
      orElse: () => NotificationType.system,
    );
  }
}

enum NotificationPriority {
  low,
  normal,
  high,
  urgent;

  String get displayName {
    switch (this) {
      case NotificationPriority.low:
        return 'Low';
      case NotificationPriority.normal:
        return 'Normal';
      case NotificationPriority.high:
        return 'High';
      case NotificationPriority.urgent:
        return 'Urgent';
    }
  }

  static NotificationPriority fromString(String value) {
    return NotificationPriority.values.firstWhere(
      (priority) => priority.name == value,
      orElse: () => NotificationPriority.normal,
    );
  }
}

class NotificationModel {
  final String id;
  final String userId;
  final String? businessId;
  final String? cashbookId;
  final NotificationType type;
  final NotificationPriority priority;
  final String title;
  final String message;
  final Map<String, dynamic>? data;
  final String? actionUrl;
  final bool isRead;
  final bool isDelivered;
  final DateTime? readAt;
  final DateTime? deliveredAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Additional fields for display
  final String? senderName;
  final String? businessName;
  final String? cashbookName;

  NotificationModel({
    required this.id,
    required this.userId,
    this.businessId,
    this.cashbookId,
    required this.type,
    this.priority = NotificationPriority.normal,
    required this.title,
    required this.message,
    this.data,
    this.actionUrl,
    this.isRead = false,
    this.isDelivered = false,
    this.readAt,
    this.deliveredAt,
    required this.createdAt,
    required this.updatedAt,
    this.senderName,
    this.businessName,
    this.cashbookName,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      businessId: json['business_id'] as String?,
      cashbookId: json['cashbook_id'] as String?,
      type: NotificationType.fromString(json['type'] as String),
      priority: NotificationPriority.fromString(json['priority'] as String? ?? 'normal'),
      title: json['title'] as String,
      message: json['message'] as String,
      data: json['data'] as Map<String, dynamic>?,
      actionUrl: json['action_url'] as String?,
      isRead: json['is_read'] as bool? ?? false,
      isDelivered: json['is_delivered'] as bool? ?? false,
      readAt: json['read_at'] != null ? DateTime.parse(json['read_at'] as String) : null,
      deliveredAt: json['delivered_at'] != null ? DateTime.parse(json['delivered_at'] as String) : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      senderName: json['sender_name'] as String?,
      businessName: json['business_name'] as String?,
      cashbookName: json['cashbook_name'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'business_id': businessId,
      'cashbook_id': cashbookId,
      'type': type.name,
      'priority': priority.name,
      'title': title,
      'message': message,
      'data': data,
      'action_url': actionUrl,
      'is_read': isRead,
      'is_delivered': isDelivered,
      'read_at': readAt?.toIso8601String(),
      'delivered_at': deliveredAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'sender_name': senderName,
      'business_name': businessName,
      'cashbook_name': cashbookName,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? userId,
    String? businessId,
    String? cashbookId,
    NotificationType? type,
    NotificationPriority? priority,
    String? title,
    String? message,
    Map<String, dynamic>? data,
    String? actionUrl,
    bool? isRead,
    bool? isDelivered,
    DateTime? readAt,
    DateTime? deliveredAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? senderName,
    String? businessName,
    String? cashbookName,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      businessId: businessId ?? this.businessId,
      cashbookId: cashbookId ?? this.cashbookId,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      title: title ?? this.title,
      message: message ?? this.message,
      data: data ?? this.data,
      actionUrl: actionUrl ?? this.actionUrl,
      isRead: isRead ?? this.isRead,
      isDelivered: isDelivered ?? this.isDelivered,
      readAt: readAt ?? this.readAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      senderName: senderName ?? this.senderName,
      businessName: businessName ?? this.businessName,
      cashbookName: cashbookName ?? this.cashbookName,
    );
  }

  bool get isUnread => !isRead;
  bool get isHighPriority => priority == NotificationPriority.high || priority == NotificationPriority.urgent;
  
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
